给nginx加
- ./certbot/conf:/etc/letsencrypt
- ./certbot/www:/var/www/certbot
docker compose up -d nginx

certbot:
image: certbot/certbot
container_name: certbot
volumes: 
  - ./certbot/conf:/etc/letsencrypt
  - ./certbot/www:/var/www/certbot
command: certonly --webroot -w /var/www/certbot --force-renewal --email {email} -d {domain} --agree-tos

在compose.yml所在的路径 mkdir -p ./certbot/www/.well-known/acme-challenge

docker compose up -d certbot

http {
    server_tokens off;
    charset utf-8;

    # always redirect to https
    server {
        listen 80 default_server;

        server_name _;

        return 301 https://$host$request_uri;
    }

    server {
        listen 443 ssl http2;
        # use the certificates
        ssl_certificate     /etc/letsencrypt/live/{domain}/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/{domain}/privkey.pem;
        server_name {domain};
        root /var/www/html;
        index index.php index.html index.htm;


        location / {
            proxy_pass http://helloworld:8000/;
        }

        location ~ /.well-known/acme-challenge/ {
            root /var/www/certbot;
        }
    }
}
crontab -e

0：表示在每小时的第0分钟执行。
5：表示在每小时的第5秒执行。
1：表示在每天的第1小时执行。
*/2：表示每隔2天执行一次。因此，这个任务将在每隔2天的第1小时第5秒执行。
*：表示在每个月的任意日期执行。
0 5 1 */2 *  /usr/bin/docker compose -f /home/<USER>/docker-compose.yml up certbot