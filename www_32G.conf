[global]
; Error log file (default: log/php-fpm.log)
error_log = /dev/null
log_level = error
[www]
; PHP内存限制和性能优化 - 针对大数据查询和16G内存
php_admin_value[memory_limit] = 2048M          ; 从4096M减少到2048M
php_admin_value[max_execution_time] = 300      ; 从900秒减少到300秒
php_admin_value[max_input_time] = 300          ; 从900秒减少到300秒
php_admin_value[post_max_size] = 512M         ; 从1024M减少到512M
php_admin_value[upload_max_filesize] = 512M   ; 从1024M减少到512M
php_admin_value[max_input_vars] = 20000        ; 从40000减少到20000
php_admin_value[realpath_cache_size] = 16M     ; 从32M减少到16M
php_admin_value[realpath_cache_ttl] = 300      ; 保持300秒

; OpCache优化 - 针对CPU使用率优化
php_admin_value[opcache.enable] = 1
php_admin_value[opcache.memory_consumption] = 512  ; 从1024M减少到512M
php_admin_value[opcache.interned_strings_buffer] = 32 ; 从64MB减少到32MB
php_admin_value[opcache.max_accelerated_files] = 50000 ; 从100000减少到50000
php_admin_value[opcache.revalidate_freq] = 60
php_admin_value[opcache.enable_file_override] = 1
php_admin_value[opcache.validate_timestamps] = 1
php_admin_value[opcache.save_comments] = 0
php_admin_value[opcache.jit] = 1205              ; 从1255调整到1205，减少JIT编译强度
php_admin_value[opcache.jit_buffer_size] = 128M  ; 从256M减少到128M

; 错误日志设置
php_flag[log_errors] = on
php_value[error_reporting] = E_ERROR
php_flag[display_errors] = off
php_value[error_log] = /var/log/php-fpm-error.log

; Listen配置
listen = 9000
listen.backlog = 8192               ; 从16384减少到8192

; Unix user/group of processes
user = www-data
group = www-data

; 日志设置 - 启用慢日志以便排查问题
access.log = /dev/null
slowlog = /var/log/php-fpm-slow.log
request_slowlog_timeout = 5s
catch_workers_output = yes

; Process manager configuration - 针对CPU使用率优化
pm = dynamic
pm.max_children = 100                ; 从160减少到100
pm.start_servers = 20                ; 从40减少到20
pm.min_spare_servers = 10            ; 从40减少到10
pm.max_spare_servers = 30            ; 从80减少到30
pm.max_requests = 1000               ; 从2000减少到1000
request_terminate_timeout = 300s     ; 从900秒减少到300秒
pm.process_idle_timeout = 10s        ; 从30秒减少到10秒

; 性能优化
pm.status_path = /status
rlimit_files = 131072               ; 从262144减少到131072
rlimit_core = 0

; 缓存优化
php_value[session.gc_maxlifetime] = 7200    ; 保持7200秒
php_value[session.gc_probability] = 1
php_value[session.gc_divisor] = 1000

; 关闭不必要的功能
php_admin_flag[allow_url_fopen] = off
php_admin_flag[mail.add_x_header] = off

; 优化文件上传
php_admin_value[max_file_uploads] = 50     ; 从100减少到50
php_admin_value[max_input_nesting_level] = 128  ; 从256减少到128

; 优化输出缓冲
php_admin_value[output_buffering] = 8192         ; 从16384减少到8192
php_flag[zlib.output_compression] = on
php_value[zlib.output_compression_level] = 1

; 健康检查
ping.path = /ping
ping.response = pong

; 数据库连接优化
php_admin_value[mysql.connect_timeout] = 30  ; 从60秒减少到30秒
php_admin_value[default_socket_timeout] = 30 ; 从60秒减少到30秒