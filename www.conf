[global]
; Error log file (default: log/php-fpm.log)
error_log = /dev/null
log_level = error
[www]
; PHP内存限制和性能优化 - 针对大数据查询和32G内存
php_admin_value[memory_limit] = 2048M
php_admin_value[max_execution_time] = 150
php_admin_value[max_input_time] = 150
php_admin_value[post_max_size] = 384M
php_admin_value[upload_max_filesize] = 384M
php_admin_value[max_input_vars] = 15000
php_admin_value[realpath_cache_size] = 64M
php_admin_value[realpath_cache_ttl] = 450

; OpCache优化 - 针对32G内存优化
php_admin_value[opcache.enable] = 1
php_admin_value[opcache.memory_consumption] = 1024
php_admin_value[opcache.interned_strings_buffer] = 64
php_admin_value[opcache.max_accelerated_files] = 70000
php_admin_value[opcache.revalidate_freq] = 90
php_admin_value[opcache.enable_file_override] = 1
php_admin_value[opcache.validate_timestamps] = 1
php_admin_value[opcache.save_comments] = 0
php_admin_value[opcache.jit] = 1230
php_admin_value[opcache.jit_buffer_size] = 128M

; 错误日志设置
php_flag[log_errors] = on
php_value[error_reporting] = E_ERROR
php_flag[display_errors] = off
php_value[error_log] = /var/log/php-fpm-error.log

; Listen配置
listen = 9000
listen.backlog = 3072

; Unix user/group of processes
user = www-data
group = www-data

; 日志设置 - 启用慢日志以便排查问题
access.log = /dev/null
slowlog = /var/log/php-fpm-slow.log
request_slowlog_timeout = 4s
catch_workers_output = yes

; Process manager configuration - 针对32G内存优化
pm = dynamic
pm.max_children = 120
pm.start_servers = 24
pm.min_spare_servers = 12
pm.max_spare_servers = 36
pm.max_requests = 1500
request_terminate_timeout = 100s
pm.process_idle_timeout = 8s

; 性能优化
pm.status_path = /status
rlimit_files = 6144
rlimit_core = 0

; 缓存优化
php_value[session.gc_maxlifetime] = 10800
php_value[session.gc_probability] = 1
php_value[session.gc_divisor] = 1500

; 关闭不必要的功能
php_admin_flag[allow_url_fopen] = off
php_admin_flag[mail.add_x_header] = off

; 优化文件上传
php_admin_value[max_file_uploads] = 60
php_admin_value[max_input_nesting_level] = 192

; 优化输出缓冲
php_admin_value[output_buffering] = 6144
php_flag[zlib.output_compression] = on
php_value[zlib.output_compression_level] = 1

; 健康检查
ping.path = /ping
ping.response = pong

; 数据库连接优化
php_admin_value[mysql.connect_timeout] = 20
php_admin_value[default_socket_timeout] = 20