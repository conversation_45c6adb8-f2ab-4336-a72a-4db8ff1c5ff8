proxy_cache_path /var/www/html/nginx_caches/shengxuwu levels=1:2 keys_zone=shengxuwu_cache:10m max_size=10g inactive=60m use_temp_path=off;

server {
    listen 80;
    server_name shengxuwu.com www.shengxuwu.com biqugego.cc www.biqugego.cc;
    
    # 默认缓存设置
    proxy_cache shengxuwu_cache;
    proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
    proxy_cache_valid 200 301 302 1h;  # 成功响应缓存1小时
    proxy_cache_key $host$request_uri;  # 缓存键包含主机名和URI

    # 静态文件不缓存
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg)$ {
        proxy_pass http://shengxuwu-app:8022;
        proxy_cache off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 后台管理不缓存
    location /rbadmin {
        proxy_pass http://shengxuwu-app:8022;
        proxy_cache off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }   
    location ~ /\.well-known/acme-challenge/ {
        root /var/www/certbot;
        allow all;        
    }  
    # 所有其他请求
    location / {
        proxy_pass http://shengxuwu-app:8022;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        proxy_cache shengxuwu_cache;
        proxy_cache_valid 200 301 302 1h;
        add_header X-Cache-Status $upstream_cache_status;  # 添加缓存状态头

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
server {
    listen 443 ssl http2;
    server_name shengxuwu.com www.shengxuwu.com biqugego.cc www.biqugego.cc;
    ssl_certificate /etc/letsencrypt/live/tukuxx.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/tukuxx.com/privkey.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    
    # 默认缓存设置
    proxy_cache shengxuwu_cache;
    proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
    proxy_cache_valid 200 301 302 1h;  # 成功响应缓存1小时
    proxy_cache_key $host$request_uri;  # 缓存键包含主机名和URI

    # 静态文件不缓存
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg)$ {
        proxy_pass http://shengxuwu-app:8022;
        proxy_cache off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 后台管理不缓存
    location /rbadmin {
        proxy_pass http://shengxuwu-app:8022;
        proxy_cache off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }   
    location ~ /\.well-known/acme-challenge/ {
        root /var/www/certbot;
        allow all;        
    }  
    # 所有其他请求
    location / {
        proxy_pass http://shengxuwu-app:8022;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        proxy_cache shengxuwu_cache;
        proxy_cache_valid 200 301 302 1h;
        add_header X-Cache-Status $upstream_cache_status;  # 添加缓存状态头

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}