server {
    listen 80;
   
    server_name rebx.net www.rebx.net;         
    root /var/www/html/rebx.net;
    index index.html index.php;  
   
    location ~ \.php$ {
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }  

    location ~ /\.well-known/acme-challenge/ {
        root /var/www/certbot;
        allow all;        
    }  

    location / {
if (!-e $request_filename){
rewrite ^/site/([1-9]+[0-9]*).html$ /dada/links.php?id=$1;
}
if (!-e $request_filename){
rewrite ^/so---(.*?).html$ /dada/search.php?text=$1;
}
if (!-e $request_filename){
rewrite ^/so.html$ /dada/search.php;
}
if (!-e $request_filename){
rewrite ^/news-([a-zA-Z0-9_.-]*).html$ /dada/article-nr.php?id=$1;
}
if ($query_string ~ "^(.*)$"){
rewrite ^/sitemap.xml$ /dada/sitemap.php;
}
if ($query_string ~ "^(.*)$"){
rewrite article/$ /dada/article.php;
}
if ($query_string ~ "^(.*)$"){
rewrite help.html$ /dada/help.php;
}
if (!-e $request_filename){
rewrite ^/image/xuanshi_link_jt_([a-zA-Z0-9_.-]*).jpg$ /include/images/timg.gif;
}
if ($query_string ~ "^(.*)$"){
rewrite ^/renqi.html$ /dada/renqi.php;
}
if ($query_string ~ "^(.*)$"){
rewrite ^/dianru.html$ /dada/dianru.php;
}
}

    client_max_body_size 50m;  
}

server {
    listen 443 ssl http2 http2;
    server_name rebx.net www.rebx.net;
    
    ssl_certificate /etc/letsencrypt/live/laikp.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/laikp.com/privkey.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    
    root /var/www/html/rebx.net;
    index index.html index.php;

    location ~ \.php$ {
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location / {
        if (!-e $request_filename){
            rewrite ^/site/([1-9]+[0-9]*).html$ /dada/links.php?id=$1;
        }
        if (!-e $request_filename){
            rewrite ^/so---(.*?).html$ /dada/search.php?text=$1;
        }
        if (!-e $request_filename){
            rewrite ^/so.html$ /dada/search.php;
        }
        if (!-e $request_filename){
            rewrite ^/news-([a-zA-Z0-9_.-]*).html$ /dada/article-nr.php?id=$1;
        }
        if ($query_string ~ "^(.*)$"){
            rewrite ^/sitemap.xml$ /dada/sitemap.php;
        }
        if ($query_string ~ "^(.*)$"){
            rewrite article/$ /dada/article.php;
        }
        if ($query_string ~ "^(.*)$"){
            rewrite help.html$ /dada/help.php;
        }
        if (!-e $request_filename){
            rewrite ^/image/xuanshi_link_jt_([a-zA-Z0-9_.-]*).jpg$ /include/images/timg.gif;
        }
        if ($query_string ~ "^(.*)$"){
            rewrite ^/renqi.html$ /dada/renqi.php;
        }
        if ($query_string ~ "^(.*)$"){
            rewrite ^/dianru.html$ /dada/dianru.php;
        }
    }

    client_max_body_size 50m;
}
