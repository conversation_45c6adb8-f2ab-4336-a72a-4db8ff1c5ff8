# Global fastcgi cache settings
fastcgi_cache_path /var/www/html/nginx_caches/81te levels=1:2 keys_zone=81te_cache:10m max_size=10g inactive=60m use_temp_path=off;
fastcgi_cache_path /var/www/html/nginx_caches/gxxxw levels=1:2 keys_zone=gxxxw_cache:10m max_size=10g inactive=60m use_temp_path=off;
fastcgi_cache_path /var/www/html/nginx_caches/hn111 levels=1:2 keys_zone=hn111_cache:10m max_size=10g inactive=60m use_temp_path=off;
fastcgi_cache_path /var/www/html/nginx_caches/tukuvod levels=1:2 keys_zone=tukuvod_cache:10m max_size=10g inactive=60m use_temp_path=off;
fastcgi_cache_path /var/www/html/nginx_caches/tt456 levels=1:2 keys_zone=tt456_cache:10m max_size=10g inactive=60m use_temp_path=off;
fastcgi_cache_path /var/www/html/nginx_caches/vvren levels=1:2 keys_zone=vvren_cache:10m max_size=10g inactive=60m use_temp_path=off;
fastcgi_cache_path /var/www/html/nginx_caches/343n levels=1:2 keys_zone=343n_cache:10m max_size=10g inactive=60m use_temp_path=off;
fastcgi_cache_path /var/www/html/nginx_caches/yq518 levels=1:2 keys_zone=yq518_cache:10m max_size=10g inactive=60m use_temp_path=off;
fastcgi_cache_path /var/www/html/nginx_caches/ll321 levels=1:2 keys_zone=ll321_cache:10m max_size=10g inactive=60m use_temp_path=off;
# ... 其他域名的cache path ...

# 全局fastcgi缓存参数
fastcgi_cache_key "$request_method$request_uri$args";
fastcgi_cache_min_uses 1;
fastcgi_cache_methods GET HEAD;
fastcgi_cache_valid 200 1h;
fastcgi_cache_use_stale error timeout updating invalid_header; 