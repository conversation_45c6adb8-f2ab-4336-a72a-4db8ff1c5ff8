server {
    listen 80;
   
    server_name tuku4.com www.tuku4.com;         
    root /var/www/html/tuku33.com;
    index index.html index.php;  
   
    location ~ \.php$ {
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }  

    location ~ /\.well-known/acme-challenge/ {
        root /var/www/certbot;
        allow all;        
    }  

    location / {
        include /etc/nginx/allowed_access.conf;
        if (!-e $request_filename) {
        rewrite ^/index.php(.*)$ /index.php?s=$1 last;
        rewrite ^/reebor.php(.*)$ /reebor.php?s=$1 last;
        rewrite ^/api.php(.*)$ /api.php?s=$1 last;
        rewrite ^(.*)$ /index.php?s=$1 last;
        break;
       }
    }

    client_max_body_size 50m;  
}
server {
    listen 443 ssl http2;
   server_name tuku4.com www.tuku4.com;      
    
    
    ssl_certificate /etc/letsencrypt/live/tukuxx.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/tukuxx.com/privkey.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    
    root /var/www/html/tuku33.com;
    index index.html index.php;

    location ~ \.php$ {
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location / {
        include /etc/nginx/allowed_access.conf;
        if (!-e $request_filename) {
            rewrite ^/index.php(.*)$ /index.php?s=$1 last;
            rewrite ^/reebor.php(.*)$ /reebor.php?s=$1 last;
            rewrite ^/api.php(.*)$ /api.php?s=$1 last;
            rewrite ^(.*)$ /index.php?s=$1 last;
            break;
        }
    }

    client_max_body_size 50m;
}
