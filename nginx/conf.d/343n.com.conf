server {
    listen 80;
    server_name 343n.com www.343n.com;         
    root /var/www/html/343n.com;
    index index.html index.php;  

    location ~ \.php$ {
        set $no_cache 0;
        
        if ($request_uri ~* "reebor") {
            set $no_cache 1;
        }
        
        fastcgi_cache 343n_cache;
        fastcgi_cache_lock on;
        fastcgi_cache_lock_timeout 5s;
        fastcgi_cache_revalidate on;
        fastcgi_cache_background_update on;
        
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param QUERY_STRING $query_string;
        fastcgi_param REQUEST_METHOD $request_method;
        fastcgi_param CONTENT_TYPE $content_type;
        fastcgi_param CONTENT_LENGTH $content_length;
        
        fastcgi_ignore_headers Cache-Control Expires Set-Cookie;
        
        fastcgi_cache_bypass $no_cache;
        fastcgi_no_cache $no_cache;
        add_header X-Cache-Status $upstream_cache_status always;

        fastcgi_pass php:9000;
        fastcgi_index index.php;
        include fastcgi_params;
    }  

    location ~ /\.well-known/acme-challenge/ {
        root /var/www/certbot;
        allow all;
    }  

    location / {
        include /etc/nginx/allowed_access.conf;
        if (!-e $request_filename) {
            rewrite ^/index.php(.*)$ /index.php?s=$1 last;
            rewrite ^/reebor.php(.*)$ /reebor.php?s=$1 last;
            rewrite ^/api.php(.*)$ /api.php?s=$1 last;
            rewrite ^(.*)$ /index.php?s=$1 last;
            break;
        }
    }

    client_max_body_size 50m;  
}

server {
    listen 443 ssl http2;
    server_name 343n.com www.343n.com;
    root /var/www/html/343n.com;
    index index.html index.php;

    ssl_certificate /etc/letsencrypt/live/gxxxw.net/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/gxxxw.net/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    ssl_stapling off;
    ssl_stapling_verify off;
    add_header Strict-Transport-Security "max-age=31536000" always;

    location ~ \.php$ {
        set $no_cache 0;
        
        if ($request_uri ~* "reebor") {
            set $no_cache 1;
        }
        
        fastcgi_cache 343n_cache;
        fastcgi_cache_lock on;
        fastcgi_cache_lock_timeout 5s;
        fastcgi_cache_revalidate on;
        fastcgi_cache_background_update on;
        
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param QUERY_STRING $query_string;
        fastcgi_param REQUEST_METHOD $request_method;
        fastcgi_param CONTENT_TYPE $content_type;
        fastcgi_param CONTENT_LENGTH $content_length;
        
        fastcgi_ignore_headers Cache-Control Expires Set-Cookie;
        
        fastcgi_cache_bypass $no_cache;
        fastcgi_no_cache $no_cache;
        add_header X-Cache-Status $upstream_cache_status always;

        fastcgi_pass php:9000;
        fastcgi_index index.php;
        include fastcgi_params;
    }

    location / {
        include /etc/nginx/allowed_access.conf;
        if (!-e $request_filename) {
            rewrite ^/index.php(.*)$ /index.php?s=$1 last;
            rewrite ^/reebor.php(.*)$ /reebor.php?s=$1 last;
            rewrite ^/api.php(.*)$ /api.php?s=$1 last;
            rewrite ^(.*)$ /index.php?s=$1 last;
            break;
        }
    }

    client_max_body_size 50m;
}
