# seekcomp.com.conf

# 定义缓存区域
proxy_cache_path /var/www/html/nginx_caches/seekcomp levels=1:2 keys_zone=seekcomp_cache:10m max_size=10g inactive=60m use_temp_path=off;

# 主站配置 (seekcomp.com 和 www.seekcomp.com)
server {
    listen 80;
    server_name seekcomp.com www.seekcomp.com;
    
    # 默认缓存设置
    proxy_cache seekcomp_cache;
    proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
    proxy_cache_valid 200 301 302 1h;  # 成功响应缓存1小时
    proxy_cache_key $host$request_uri;  # 缓存键包含主机名和URI

    # categorys 缓存一个月
    location /categorys {
        proxy_pass http://seekcomp-app:8091;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        proxy_cache seekcomp_cache;
        proxy_cache_valid 200 301 302 30d;  # 缓存一个月
        add_header X-Cache-Status $upstream_cache_status;
    }

    # cate/* 缓存一个月
    location ~ ^/cate/ {
        proxy_pass http://seekcomp-app:8091;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        proxy_cache seekcomp_cache;
        proxy_cache_valid 200 301 302 30d;  # 缓存一个月
        add_header X-Cache-Status $upstream_cache_status;
    }

    # allbrand 缓存一个月
    location /allbrand {
        proxy_pass http://seekcomp-app:8091;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        proxy_cache seekcomp_cache;
        proxy_cache_valid 200 301 302 30d;  # 缓存一个月
        add_header X-Cache-Status $upstream_cache_status;
    }

    # photos 缓存半个月
    location /photos {
        proxy_pass http://seekcomp-app:8091;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        proxy_cache seekcomp_cache;
        proxy_cache_valid 200 301 302 15d;  # 缓存半个月
        add_header X-Cache-Status $upstream_cache_status;
    }

    # photo/* 缓存半个月
    location ~ ^/photo/ {
        proxy_pass http://seekcomp-app:8091;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        proxy_cache seekcomp_cache;
        proxy_cache_valid 200 301 302 15d;  # 缓存半个月
        add_header X-Cache-Status $upstream_cache_status;
    }

    # article/* 缓存一周
    location ~ ^/article/ {
        proxy_pass http://seekcomp-app:8091;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        proxy_cache seekcomp_cache;
        proxy_cache_valid 200 301 302 7d;  # 缓存一周
        add_header X-Cache-Status $upstream_cache_status;
    }

    # page/* 缓存一周
    location ~ ^/page/ {
        proxy_pass http://seekcomp-app:8091;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        proxy_cache seekcomp_cache;
        proxy_cache_valid 200 301 302 7d;  # 缓存一周
        add_header X-Cache-Status $upstream_cache_status;
    }

    # product/* 缓存一个月
    location ~ ^/product/ {
        proxy_pass http://seekcomp-app:8091;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        proxy_cache seekcomp_cache;
        proxy_cache_valid 200 301 302 30d;  # 缓存一个月
        add_header X-Cache-Status $upstream_cache_status;
    }

    # 静态文件不缓存
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg)$ {
        proxy_pass http://seekcomp-app:8091;
        proxy_cache off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 后台管理不缓存
    location /rbadmin {
        proxy_pass http://seekcomp-app:8091;
        proxy_cache off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 报价相关不缓存
    location /quote {
        proxy_pass http://seekcomp-app:8091;
        proxy_cache off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 其他所有请求缓存1小时
    location / {
        proxy_pass http://seekcomp-app:8091;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        proxy_cache seekcomp_cache;
        proxy_cache_valid 200 301 302 1h;
        add_header X-Cache-Status $upstream_cache_status;  # 添加缓存状态头

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# 子域名配置 (*.seekcomp.com，但不包括 www.seekcomp.com 和 seekcomp.com)
server {
    listen 80;
    server_name ~^(?!www\.seekcomp\.com|seekcomp\.com).*\.seekcomp\.com$;
    
    # 域名重定向规则
    if ($host = 'delphi.seekcomp.com') {
        return 301 $scheme://aptiv.seekcomp.com$request_uri;
    }
    if ($host = 'ti.seekcomp.com') {
        return 301 $scheme://texas-instruments.seekcomp.com$request_uri;
    }
    if ($host = 'te.seekcomp.com') {
        return 301 $scheme://te-connectivity.seekcomp.com$request_uri;
    }
    
    # 默认缓存设置
    proxy_cache seekcomp_cache;
    proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
    proxy_cache_valid 200 301 302 30d;  # 子域名缓存半个月
    proxy_cache_key $host$request_uri;  # 缓存键包含主机名和URI

    # 静态文件不缓存
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg)$ {
        proxy_pass http://seekcomp-app:8091;
        proxy_cache off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 后台管理不缓存
    location /rbadmin {
        proxy_pass http://seekcomp-app:8091;
        proxy_cache off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 报价相关不缓存
    location /quote {
        proxy_pass http://seekcomp-app:8091;
        proxy_cache off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 其他所有请求缓存半个月
    location / {
        proxy_pass http://seekcomp-app:8091;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存设置
        proxy_cache seekcomp_cache;
        proxy_cache_valid 200 301 302 15d;  # 子域名缓存半个月
        add_header X-Cache-Status $upstream_cache_status;  # 添加缓存状态头        

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}