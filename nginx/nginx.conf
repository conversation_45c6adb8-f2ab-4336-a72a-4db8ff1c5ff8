user nginx;
worker_processes auto;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    access_log off;
    error_log /dev/null crit;
    map $http_user_agent $mobile {
        default 0;
        "~*android" 1;
        "~*iphone" 1;
        "~*ipad" 1;
        "~*ipod" 1;
        "~*blackberry" 1;
        "~*windows\sphone" 1;
        "~*opera\smini" 1;
        "~*opera\smobi" 1;
        "~*kindle" 1;
        "~*mobile" 1;
        "~*tablet" 1;
        "~*webos" 1;
        "~*samsung" 1;
        "~*huawei" 1;
        "~*xiaomi" 1;
        "~*vivo" 1;
        "~*oppo" 1;
    }
    map $http_user_agent $search_engine {
        default 0;        
        "~*googlebot" 1;
        "~*bingbot" 1;
        "~*bing" 1;
        "~*yahoo" 1;
        "~*yandex" 1;
        "~*baidu" 1;
        "~*sogou" 1;
        "~*360spider" 1;
        "~*so.com" 1;      
        "~*sm.cn" 1;       
        "~*toutiao" 1;
        "~*duckduckgo" 1;
        "~*archive.org_bot" 1;
        "~*semrushbot" 1;
        "~*ahrefs" 1;
        "~*mj12bot" 1;
        "~*dotbot" 1;
        "~*applebot" 1;
        "~*slurp" 1;      
        "~*baiduspider" 1;
        "~*seznambot" 1;
    }
    map $http_accept $mobile_accept {
    default 0;
    "~*application/vnd.wap.xhtml+xml" 1;
    "~*text/vnd.wap.wml" 1;
    "~*application/vnd.wap.wmlc" 1;
    }
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    gzip on;
    gzip_disable "msie6";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    include /etc/nginx/conf.d/*.conf;    
}
