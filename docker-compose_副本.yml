version: '3.8'
services:
  nginx:
    image: nginx:1.24.0
    container_name: nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"   
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/allowed_access.conf:/etc/nginx/allowed_access.conf
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
      - ./html:/var/www/html
      - ./hdhtml:/var/www/hdhtml
  php:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: php
    restart: always
    volumes:
      - ./html:/var/www/html
      - ./hdhtml:/var/www/hdhtml
      - ./www.conf:/usr/local/etc/php-fpm.d/www.conf
  mysql:
    image: mysql:8.0
    container_name: mysql
    restart: always
    volumes:
      - ./mysql:/var/lib/mysql
      - ./my.cnf:/etc/mysql/conf.d/my.cnf
    environment:
      MYSQL_ROOT_PASSWORD: ReeB588sql
      MYSQL_USER: reebor
      MYSQL_PASSWORD: rbsql88688
  redis:
    image: redis:7.2.4
    container_name: redis
    restart: always
    volumes:
      - ./redis:/data
  trojan-go:
    image: p4gefau1t/trojan-go
    container_name: trojan-go
    volumes:
       - ./certbot/conf:/etc/letsencrypt
       - ./certbot/www:/var/www/certbot
       - ./trojan-go/:/etc/trojan-go
    ports:
      - "9443:443"   
    restart: unless-stopped
  certbot:
    image: certbot/certbot
    container_name: certbot
    volumes: 
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    command: certonly --non-interactive --webroot -w /var/www/certbot --force-renewal --email <EMAIL> 
      -d tukuxx.com -d www.tukuxx.com
      -d tukuyy.com -d www.tukuyy.com
      -d tuku33.com -d www.tuku33.com
      -d 8baotv.com -d www.8baotv.com
      -d xddbb.com -d www.xddbb.com  
      -d albbb.com -d www.albbb.com 
      -d n1.img.cdn.kv1z.com -d n2.img.cdn.kv1z.com
      -d n3.img.cdn.kv1z.com -d n4.img.cdn.kv1z.com    
      --agree-tos
  certbot2:
    image: certbot/certbot
    container_name: certbot2
    volumes: 
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    command: certonly --non-interactive --webroot -w /var/www/certbot --force-renewal --email <EMAIL> -d getfysj1.dizan.net --agree-tos