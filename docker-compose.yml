version: '3.8'
services:
  nginx:
    image: nginx:1.26.3
    container_name: nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"   
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/allowed_access.conf:/etc/nginx/allowed_access.conf
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
      - ./html:/var/www/html
      - ./nginx_caches:/var/www/html/nginx_caches
      - ./nginx_caches:/var/www/html/nginx_caches/shengxuwu
  php:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: php
    restart: always
    volumes:
      - ./html:/var/www/html
      - ./www.conf:/usr/local/etc/php-fpm.d/www.conf
  mysql:
    image: mysql:8.0
    container_name: mysql
    restart: always
    volumes:
      - ./mysql:/var/lib/mysql
      - ./my.cnf:/etc/mysql/conf.d/my.cnf
    environment:
      MYSQL_ROOT_PASSWORD: ReeB588sql
      MYSQL_USER: reebor
      MYSQL_PASSWORD: rbsql88688
  redis:
    image: redis:7.2.4
    container_name: redis
    restart: always
    volumes:
      - ./redis:/data
    deploy:
      resources:
        limits:
          memory: 8G
        reservations:
          memory: 1G
  trojan-go:
    image: p4gefau1t/trojan-go
    container_name: trojan-go
    volumes:
       - ./certbot/conf:/etc/letsencrypt
       - ./certbot/www:/var/www/certbot
       - ./trojan-go/:/etc/trojan-go
    ports:
      - "9443:443"   
    restart: unless-stopped
  certbot:
    image: certbot/certbot
    container_name: certbot
    volumes: 
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    command: certonly --non-interactive --webroot -w /var/www/certbot --force-renewal --email <EMAIL>          
      -d 48ji.com -d www.48ji.com
      -d 6388016.com -d www.6388016.com      
      -d cechix.com -d www.cechix.com
      -d danxiaoxi.com -d www.danxiaoxi.com
      -d dinameizhuang.com -d www.dinameizhuang.com
      -d disiok.com -d www.disiok.com     
      -d fw14.com -d www.fw14.com
      -d gottyy.com -d www.gottyy.com    
      -d hupov.com -d www.hupov.com      
      -d tulin.cc -d www.tulin.cc
      --agree-tos

  certbot2:
    image: certbot/certbot
    container_name: certbot2
    volumes: 
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    command: certonly --non-interactive --webroot -w /var/www/certbot --force-renewal --email <EMAIL>   
      -d laikp.com -d www.laikp.com
      -d lia4.com -d www.lia4.com
      -d mengpian.cc -d www.mengpian.cc
      -d rebx.net -d www.rebx.net
      -d riluu.com -d www.riluu.com
      -d sidiyy.com -d www.sidiyy.com
      -d st1122.com -d www.st1122.com
      -d ipiii.com -d www.ipiii.com
      -d td876.com -d www.td876.com
      -d tl654.com -d www.tl654.com   
      -d tuoniao123.com -d www.tuoniao123.com     
      -d xaodi.com -d www.xaodi.com           
      -d xi15.com -d www.xi15.com
      -d xyd78.com -d www.xyd78.com     
      -d ypccc.com -d www.ypccc.com
      -d yuelanwu.com -d www.yuelanwu.com
      --agree-tos

  certbot3:
    image: certbot/certbot
    container_name: certbot3
    volumes: 
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    command: certonly --non-interactive --webroot -w /var/www/certbot --force-renewal --email <EMAIL> 
      -d zyyy.cc -d www.zyyy.cc
      -d zzyun.net -d www.zzyun.net  
      -d 348n.com -d www.348n.com   
      -d moffox.com -d www.moffox.com
      -d saodong.cc -d www.saodong.cc
      -d cdn234.com -d www.cdn234.com
      --agree-tos
  certbot4:
    image: certbot/certbot
    container_name: certbot4
    volumes: 
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    command: certonly --non-interactive --webroot -w /var/www/certbot --force-renewal --email <EMAIL> -d gxxxw.net -d www.gxxxw.net -d 343n.com -d www.343n.com -d tt456.cc -d www.tt456.cc -d tukuvod.com -d www.tukuvod.com -d ll321.cc -d www.ll321.cc -d yq518.com -d www.yq518.com -d 81te.com -d www.81te.com -d hn111.cc -d www.hn111.cc -d 3040x.com -d www.3040x.com -d vvren.com -d www.vvren.com -d 8tv8.com -d www.8tv8.com -d kumoka.com -d www.kumoka.com -d ylw123.cc -d www.ylw123.cc -d yulanwu.cc -d www.yulanwu.cc --agree-tos
  certbot5:
    image: certbot/certbot
    container_name: certbot5
    volumes: 
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    command: certonly --non-interactive --webroot -w /var/www/certbot --force-renewal --email <EMAIL> -d tllss.com -d www.tllss.com -d xf111.cc -d www.xf111.cc -d xf321.cc -d www.xf321.cc -d cef2.com -d www.cef2.com -d xf543.com -d www.xf543.com -d ws123.cc -d www.ws123.cc -d youyetv.com -d www.youyetv.com -d ehyy.cc -d www.ehyy.cc -d dy321.cc -d www.dy321.cc -d qqqdy.com -d aiqi8.com -d www.aiqi8.com  -d albbb.com -d www.albbb.com -d www.qqqdy.com -d shengxuwu.com -d www.shengxuwu.com -d biqugego.cc -d www.biqugego.cc --agree-tos
  certbot6:
    image: certbot/certbot
    container_name: certbot6
    volumes: 
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    command: certonly --non-interactive --webroot -w /var/www/certbot --force-renewal --email <EMAIL> -d yinet.co -d www.yinet.co -d en.yinet.co --agree-tos
  certbot7:
    image: certbot/certbot
    container_name: certbot7
    volumes: 
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    command: certonly --non-interactive --webroot -w /var/www/certbot --force-renewal --email <EMAIL> 
      -d tukuxx.com -d www.tukuxx.com
      -d tuku4.com -d www.tuku4.com
      -d tukuyy.com -d www.tukuyy.com
      -d tuku33.com -d www.tuku33.com
      -d 8baotv.com -d www.8baotv.com
      -d 49pin.com -d www.49pin.com
      -d dizhii.com -d www.dizhii.com          
      -d guobaotv.com -d www.guobaotv.com
      -d xmhww.com -d www.xmhww.com  
      -d xingkong6.com -d www.xingkong6.com      
      -d xf456.cc -d www.xf456.cc
      -d xf234.com -d www.xf234.com 
      -d bh-jdq.com -d www.bh-jdq.com
      -d yuanzundian.com -d www.yuanzundian.com  
      -d biqugenew.cc -d www.biqugenew.cc
      -d n3.img.cdn.kv1z.com 
      --agree-tos 
  yuanzundian-app:
    image: golang:1.23.9
    container_name: yuanzundian-app
    restart: always
    volumes:
      - ./html/yuanzundian-app:/app/yuanzundian
    working_dir: /app/yuanzundian
    command: sh -c "go mod tidy && go run cmd/main.go"
  shengxuwu-app:
    image: golang:1.23.9
    container_name: shengxuwu-app
    restart: always
    volumes:
      - ./html/shengxuwu-app:/app/shengxuwu
    working_dir: /app/shengxuwu
    command: sh -c "go mod tidy && go run cmd/main.go"
  seekcomp-app:
    image: golang:1.23.9
    container_name: seekcomp-app
    restart: always
    volumes:
      - ./html/seekcomp-app:/app/seekcomp
    working_dir: /app/seekcomp
    command: sh -c "go mod tidy && go run main.go"
  aiartices:
    image: golang:1.23.9
    container_name: aiartices
    restart: always
    volumes:
      - ./html/aiartices:/app/aiartices
    working_dir: /app/aiartices
    command: sh -c "go mod tidy && go run main.go"
  
 
