[mysqld]
# MyISAM 核心配置 - 适应32G内存，优化查询速度
key_buffer_size = 12G                 # 从6G增加到12G
myisam_sort_buffer_size = 2G          # 从1G增加到2G
myisam_use_mmap = 1                   # 保持使用内存映射
myisam_recover_options = BACKUP,FORCE # 保持恢复选项
myisam_max_sort_file_size = 16G       # 从8G增加到16G
myisam_block_size = 4K                # 保持块大小
ft_min_word_len = 2                   # 保持全文索引最小词长
low_priority_updates = 1              # 保持降低写操作优先级
preload_buffer_size = 1G              # 从512M增加到1G

# MyISAM特定优化
concurrent_insert = 2                 # 保持并发插入设置
delay_key_write = OFF                 # 保持关闭延迟键写入
myisam_stats_method = nulls_unequal   # 保持统计数据优化
myisam_data_pointer_size = 7          # 保持指针大小

# 查询缓冲配置 - 适应32G内存
tmp_table_size = 4G                   # 从2G增加到4G
max_heap_table_size = 4G              # 从2G增加到4G

# 查询相关缓冲区 - 适应32G内存
sort_buffer_size = 512M               # 从256M增加到512M
read_buffer_size = 512M               # 从256M增加到512M
read_rnd_buffer_size = 512M           # 从256M增加到512M
join_buffer_size = 512M               # 从256M增加到512M

# 连接和线程 - 适应16核CPU
thread_stack = 512K                   # 保持线程栈大小
thread_cache_size = 512               # 保持线程缓存大小
table_open_cache = 64000              # 从32000增加到64000
max_connections = 2000                # 从1000增加到2000

# 完全关闭日志，减少IO
general_log = 0
slow_query_log = 0
skip-log-bin

# 主机缓存设置 - 处理"--skip-host-cache is deprecated"警告
host_cache_size = 0                   # 用设置为0替代skip-host-cache

# MySQL 8.0优化参数 - 适应16核CPU，优化MyISAM查询
optimizer_switch = 'index_merge=on,index_merge_union=on,index_merge_sort_union=on,index_merge_intersection=on,engine_condition_pushdown=on,index_condition_pushdown=on,mrr=on,mrr_cost_based=off,block_nested_loop=on,batched_key_access=on,materialization=on,semijoin=on,loosescan=on,firstmatch=on,duplicateweedout=off,subquery_materialization_cost_based=on,use_index_extensions=on,condition_fanout_filter=on,derived_merge=on'
optimizer_search_depth = 0            # 保持自动选择优化深度
table_definition_cache = 64000        # 从32000增加到64000
eq_range_index_dive_limit = 16000     # 从8000增加到16000

# 性能优化 - 适应16核CPU
max_prepared_stmt_count = 256000      # 从128000增加到256000
performance_schema = OFF              # 保持关闭性能模式
table_open_cache_instances = 32       # 保持32个实例

# CPU使用控制
max_execution_time = 180000           # 保持180秒
max_seeks_for_key = 4096              # 从2048增加到4096

# InnoDB参数 - 适应32G内存和16核CPU
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_buffer_pool_size = 12G         # 从6G增加到12G
innodb_buffer_pool_instances = 16     # 保持16实例
innodb_flush_neighbors = 0            # 保持禁用相邻页刷新
innodb_read_io_threads = 16           # 保持16线程
innodb_write_io_threads = 16          # 保持16线程
innodb_io_capacity = 20000            # 从10000增加到20000
innodb_io_capacity_max = 40000        # 从20000增加到40000
innodb_lru_scan_depth = 1024          # 从512增加到1024
innodb_lock_wait_timeout = 30         # 保持锁等待超时
innodb_spin_wait_delay = 6            # 保持自旋等待延迟
innodb_autoinc_lock_mode = 2          # 保持交错锁模式
innodb_print_all_deadlocks = 1        # 保持记录死锁信息
innodb_strict_mode = 1                # 保持严格模式
innodb_thread_concurrency = 32        # 保持32线程
innodb_adaptive_hash_index = ON       # 保持自适应哈希索引
innodb_change_buffering = all         # 保持所有更改缓冲
innodb_stats_on_metadata = 0          # 保持禁用元数据统计
innodb_max_dirty_pages_pct = 90       # 保持脏页比例
innodb_max_dirty_pages_pct_lwm = 10   # 保持低水位线

# 网络相关参数
max_allowed_packet = 512M             # 从256M增加到512M
net_buffer_length = 4M                # 从2M增加到4M
back_log = 8192                       # 从4096增加到8192
interactive_timeout = 600             # 保持交互超时
wait_timeout = 600                    # 保持等待超时

# 优化器和查询相关
range_optimizer_max_mem_size = 1G     # 从512M增加到1G
optimizer_prune_level = 1             # 保持优化器剪枝
optimizer_trace_limit = 1             # 保持优化器跟踪限制

# Socket配置
socket = /var/run/mysqld/mysqld.sock  # 保持socket路径

# 安全配置 - 解决CA证书自签名警告
ssl = OFF                             # 关闭SSL，如不需要加密连接