<?php
header("Content-type: text/html; charset=utf-8");

function translate($q='',$tl='zh-CN',$sl='auto'){
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL,'https://translate.google.com/translate_a/single?client=at&dt=t&dj=1&ie=UTF-8');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(array('sl'=>$sl,'tl'=>$tl,'q'=>$q)));
    //curl_setopt($ch, CURLOPT_PROXY, "*************:8080");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'AndroidTranslate/5.8');
    $result = curl_exec($ch);
    curl_close($ch);

    $json = json_decode($result);
    if(empty($json->sentences)) return $q;
    $arr = array_map(function($v){ return $v->trans; },$json->sentences);
    return implode($arr);
}
function fy($query,$tl){
    $fyco=translate($query, $tl);
    return $fyco;
}
//示例：把文字翻译成英文
$tl = $_POST['tl'];
$text=$_POST['txt'];
print_r(fy($text,$tl));
?>