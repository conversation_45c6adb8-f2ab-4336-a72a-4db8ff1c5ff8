<?php

class GoogleAI {
    private $apiKey;
    private $apiEndpoint = 'https://generativelanguage.googleapis.com/v1beta/models/';
    private $model = 'gemini-2.0-flash';

    public function __construct($apiKey) {
        $this->apiKey = $apiKey;
    }

    public function setModel($model) {
        $this->model = $model;
        return $this;
    }

    public function generateContent($prompt) {
        $url = $this->apiEndpoint . $this->model . ':generateContent?key=' . $this->apiKey;
        
        $data = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => $prompt
                        ]
                    ]
                ]
            ]
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return [
                'success' => false,
                'error' => $error
            ];
        }

        if ($httpCode !== 200) {
            return [
                'success' => false,
                'error' => 'HTTP Error: ' . $httpCode,
                'response' => $response
            ];
        }

        return [
            'success' => true,
            'data' => json_decode($response, true)
        ];
    }
}

// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 检查是否是通过subcon参数请求
    if (isset($_POST['subcon'])) {
        $prompt = $_POST['subcon'];
        $apiKey = 'AIzaSyCCCkRpP-2ujIj96zhOfLosEZ0SHUQS7kA';

        if (empty($prompt)) {
            echo '内容不能为空';
            exit;
        }

        $ai = new GoogleAI($apiKey);
        $result = $ai->generateContent($prompt);
        
        if ($result['success'] && isset($result['data']['candidates'][0]['content']['parts'][0]['text'])) {
            // 直接输出文本内容
            echo $result['data']['candidates'][0]['content']['parts'][0]['text'];
        } else {
            echo $result['error'] ?? '生成失败';
        }
        exit;
    }
    
    // 处理JSON格式的请求（用于网页界面）
    $input = json_decode(file_get_contents('php://input'), true);
    $prompt = $input['prompt'] ?? '';
    $apiKey = 'AIzaSyCCCkRpP-2ujIj96zhOfLosEZ0SHUQS7kA';

    if (empty($prompt)) {
        echo json_encode([
            'success' => false,
            'error' => 'Prompt is required'
        ]);
        exit;
    }

    $ai = new GoogleAI($apiKey);
    $result = $ai->generateContent($prompt);
    
    echo json_encode($result);
    exit;
}

// 如果是GET请求，显示测试页面
?>
<!-- <!DOCTYPE html>
<html>
<head>
    <title>Google AI API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
        }
        #response {
            white-space: pre-wrap;
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            display: none;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <h1>Google AI API Test</h1>
    <textarea id="prompt" placeholder="输入您的问题..."></textarea>
    <button onclick="sendRequest()">发送请求</button>
    <div id="response"></div>

    <script>
        function sendRequest() {
            const prompt = document.getElementById('prompt').value;
            const responseDiv = document.getElementById('response');
            
            if (!prompt) {
                alert('请输入问题');
                return;
            }

            responseDiv.style.display = 'block';
            responseDiv.textContent = '请求中...';

            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ prompt })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.candidates && data.data.candidates[0]) {
                    // 直接显示文本内容
                    const text = data.data.candidates[0].content.parts[0].text;
                    responseDiv.textContent = text;
                } else {
                    responseDiv.textContent = '错误: ' + (data.error || '未知错误');
                }
            })
            .catch(error => {
                responseDiv.textContent = '请求失败: ' + error.message;
            });
        }
    </script>
</body>
</html>  -->