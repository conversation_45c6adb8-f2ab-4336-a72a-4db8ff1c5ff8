# 使用官方 PHP 7.4 镜像作为基础镜像
FROM php:7.4-fpm

# 更新包列表并安装所需的依赖
RUN apt-get update && apt-get install -y \
    libzip-dev \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    && docker-php-ext-install -j$(nproc) mysqli pdo_mysql zip opcache \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd

# 安装 Redis 扩展
RUN pecl install redis \
    && docker-php-ext-enable redis