# 使用官方 PHP 7.4 镜像作为基础镜像
FROM php:7.4.33-fpm

# 更新包列表并安装所需的依赖
RUN apt-get update && apt-get install -y \
    libzip-dev \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    default-mysql-client \
    default-libmysqlclient-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装PHP扩展
RUN docker-php-ext-install -j$(nproc) zip opcache mysqli \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd pdo_mysql

# 安装 Redis 扩展
RUN pecl install redis \
    && docker-php-ext-enable redis

# 创建日志目录
RUN mkdir -p /var/log && \
    touch /var/log/php-fpm-error.log /var/log/php-fpm-slow.log && \
    chmod 666 /var/log/php-fpm-error.log /var/log/php-fpm-slow.log

# 验证安装 - 使用 || true 避免因某个扩展不在 php -m 输出中而失败
RUN php -m | grep -E 'pdo|mysql|gd|redis|zip' || true && \
    php --ri opcache | grep -q "opcache.enable => On" || echo "Opcache installed but not enabled in CLI"