<?php
// 定义URL地址数组
$url = [        
    'http://getfy1.dizan.net',  
    'http://getfyspart.dizan.net',
    'http://getfywho.dizan.net',
    'http://getfycc.dizan.net',
    'http://getfytj.dizan.net',
];

// 从文件中读取当前URL索引
$indexFilePath = 'current_url_index.txt';
if (file_exists($indexFilePath)) {
    $currentUrlIndex = (int) file_get_contents($indexFilePath);
} else {
    $currentUrlIndex = 0;
}

function getfy($txt, $tl, $url) {
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url.'/getgg.php');
    curl_setopt($curl, CURLOPT_HEADER, 0);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_POST, 1);
    curl_setopt($curl, CURLOPT_TIMEOUT, 5);  // 添加超时设置
    
    $post_data = array(
        "txt" => $txt,
        "tl" => $tl,
    );
    curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);
    
    $data = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    return ['data' => $data, 'httpCode' => $httpCode];
}

function tryNextUrl($urls, &$currentIndex, $txt, $tl) {
    $maxAttempts = count($urls);  // 最大尝试次数
    $attempts = 0;
    
    while ($attempts < $maxAttempts) {
        $urlToTry = $urls[$currentIndex];
        $result = getfy($txt, $tl, $urlToTry);
        
        if ($result['httpCode'] == 200) {
            // 保存成功的索引
            file_put_contents('current_url_index.txt', $currentIndex);
            return $result['data'];
        }
        
        // 如果失败，尝试下一个URL
        $currentIndex = ($currentIndex + 1) % count($urls);
        $attempts++;
    }
    
    return false;  // 所有URL都失败
}

$txt = $_POST['txt'];
$tl = $_POST['tl'];

// 尝试获取翻译结果
$result = tryNextUrl($url, $currentUrlIndex, $txt, $tl);

if ($result !== false) {
    echo $result;
} else {
    echo json_encode(['error' => 'All translation services are unavailable']);
}
?>