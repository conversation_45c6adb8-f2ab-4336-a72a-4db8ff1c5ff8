[global]
; Error log file (default: log/php-fpm.log)
error_log = /dev/null
log_level = error
[www]
; -- 资源限制 --
; 根据应用实际需要调整，512M通常足够，甚至256M，需要监控确认
php_admin_value[memory_limit] = 512M
php_admin_value[max_execution_time] = 60      ; 如果应用通常很快，降到60秒，甚至30秒
php_admin_value[max_input_time] = 60       ; 同上
php_admin_value[post_max_size] = 128M      ; 根据实际最大上传需求调整
php_admin_value[upload_max_filesize] = 128M ; 根据实际最大上传需求调整
php_admin_value[max_input_vars] = 10000     ; 除非有超大表单，否则10000足够，甚至更低
php_admin_value[realpath_cache_size] = 32M     ; 适当增加，对于大型项目有益
php_admin_value[realpath_cache_ttl] = 600      ; 增加缓存时间

; -- OpCache 优化 --
php_admin_value[opcache.enable] = 1
php_admin_value[opcache.memory_consumption] = 512  ; 保持或根据opcache状态页观察适当调整
php_admin_value[opcache.interned_strings_buffer] = 64 ; 对于大型应用，可以适当增加
php_admin_value[opcache.max_accelerated_files] = 65407 ; 接近素数通常更好，根据实际文件数量调整，`find . -type f -name "*.php" | wc -l` 估算
php_admin_value[opcache.revalidate_freq] = 0       ; 生产环境设为0，配合部署脚本清除缓存
php_admin_value[opcache.validate_timestamps] = 0   ; 生产环境设为0，提升性能
php_admin_value[opcache.save_comments] = 1         ; **重要:** 设为1以兼容大多数现代框架，除非你确认应用不需要注释
php_admin_value[opcache.enable_file_override] = 1
php_admin_value[opcache.jit] = tracing             ; PHP 7.4推荐 'tracing' 或 'function'。1205是tracing模式下的一个特定设置组合。直接用 'tracing' 更通用。
php_admin_value[opcache.jit_buffer_size] = 256M  ; JIT Buffer可以适当增大

; -- 错误与日志 --
php_flag[log_errors] = on
; 生产环境建议 E_WARNING 或 E_ALL & ~E_DEPRECATED & ~E_STRICT
php_value[error_reporting] = E_WARNING
php_flag[display_errors] = off
php_value[error_log] = /var/log/php-fpm-error.log ; 确保这个路径在容器内可写

; -- Listen 配置 --
listen = 9000
; listen = /var/run/php-fpm.sock ; 如果Nginx在同一容器或通过volume共享，socket通常性能更好
; listen.owner = www-data
; listen.group = www-data
; listen.mode = 0660
listen.backlog = 4096 ; 通常不需要特别大，除非瞬时连接数极高，可以从4096开始观察

; -- 进程用户/组 --
user = www-data
group = www-data

; -- 日志设置 --
; access.log = /var/log/php-fpm-access.log ; 如果需要FPM级别的访问日志（通常由Web服务器记录）
access.log = /proc/self/fd/2 ; 输出到stderr，方便Docker收集
slowlog = /var/log/php-fpm-slow.log
request_slowlog_timeout = 5s ; 监控执行超过5秒的脚本
request_slowlog_trace_depth = 20 ; 增加堆栈深度
catch_workers_output = yes

; -- 进程管理器 (PM) - 基于64GB内存和假设迁移到InnoDB后的优化 --
; **重要:** 这个设置需要根据实际情况反复调优
pm = dynamic
; 计算: 假设系统保留 8GB, MySQL 使用 16GB (需根据MySQL配置调整), 剩余约 40GB 给PHP-FPM
; 假设平均PHP进程占用 100MB (需要实际监控确认！)
; 最大进程数理论值: 40 * 1024 / 100 = 409
; 考虑到CPU核心数(64线程)，可以设置一个较高的值，但需监控CPU和内存使用率
; 先从一个相对保守但比当前高的值开始，例如 256
pm.max_children = 256
; start_servers 建议为核心数的 1-2 倍 (逻辑核心)
pm.start_servers = 64
; min_spare_servers 建议为核心数的 0.5-1 倍
pm.min_spare_servers = 32
; max_spare_servers 建议为核心数的 1-2 倍
pm.max_spare_servers = 128
; 每个子进程处理多少个请求后重启，有助于释放内存泄漏（如果有的话）
pm.max_requests = 1000
; 如果 max_execution_time 调整了，这里也建议同步调整
request_terminate_timeout = 60s
pm.process_idle_timeout = 15s ; 空闲进程存活时间，可以适当增加

; -- 性能与限制 --
pm.status_path = /status ; 开启状态页，用于监控
; rlimit_files = 131072 ; 保持或根据 ulimit -n 调整，确保系统限制足够
rlimit_files = 65535 ; 除非确认需要超多文件句柄，否则65535通常足够且更安全
rlimit_core = 0

; -- Session --
; 保持默认或根据需求调整
php_value[session.gc_maxlifetime] = 7200
php_value[session.gc_probability] = 1
php_value[session.gc_divisor] = 1000

; -- 安全与杂项 --
php_admin_flag[allow_url_fopen] = off ; 保持关闭，除非确实需要
php_admin_flag[mail.add_x_header] = off

; -- 文件上传 --
php_admin_value[max_file_uploads] = 20     ; 根据应用需求调整
php_admin_value[max_input_nesting_level] = 64  ; 默认值通常足够，除非有深度嵌套的表单数据

; -- 输出缓冲与压缩 --
php_admin_value[output_buffering] = 4096         ; 4K是常见默认值且通常足够
php_flag[zlib.output_compression] = on
php_value[zlib.output_compression_level] = 5      ; 压缩级别1-9，5是性能和效果的较好平衡点

; -- 健康检查 --
ping.path = /ping
ping.response = pong

; -- 数据库连接 (PHP侧) --
; 这些是PHP连接MySQL时的超时，不是MySQL服务器本身的超时
php_admin_value[mysql.connect_timeout] = 5   ; 连接超时可以设置短一些
php_admin_value[default_socket_timeout] = 60  ; 默认套接字超时，影响所有基于socket的流操作，包括MySQL的长查询等待，保持60s或根据max_execution_time调整