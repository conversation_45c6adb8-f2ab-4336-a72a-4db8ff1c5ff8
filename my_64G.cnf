[mysqld]
port = 3306
# bind-address = 0.0.0.0 # Or specific IP if needed
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
default-storage-engine = MyISAM # Ensure MyISAM is default

# -- MyISAM Core - Maximize Index Caching --
key_buffer_size = 40G
myisam_sort_buffer_size = 2G
myisam_use_mmap = 1
myisam_recover_options = BACKUP,FORCE
myisam_max_sort_file_size = 100G
ft_min_word_len = 2
low_priority_updates = 1          # Prioritize reads over writes
preload_buffer_size = 64M
concurrent_insert = 2
delay_key_write = OFF             # Data safety first
myisam_stats_method = nulls_unequal

# -- Per-Thread Buffers - KEEP SMALL! --
sort_buffer_size = 8M
read_buffer_size = 4M
read_rnd_buffer_size = 8M
join_buffer_size = 8M

# -- Memory Temporary Tables --
tmp_table_size = 4G
max_heap_table_size = 4G

# -- Connections & Threading --
max_connections = 1000
thread_stack = 512K
thread_cache_size = 128
skip-name-resolve                 # Faster connections if grants use IPs
host_cache_size = 0               # Correct way to disable host cache (replaces --skip-host-cache)

# -- Table Caches --
table_open_cache = 8192
table_definition_cache = 8192
table_open_cache_instances = 32

# -- Logging - DISABLED (High Risk!) --
# log_error = /var/log/mysql/error.log # STRONGLY RECOMMENDED TO ENABLE THIS!
general_log = 0
slow_query_log = 0
skip-log-bin                      # WARNING: Disables recovery & replication!

# -- InnoDB Settings - Minimized --
# Keep a minimal footprint for internal/system tables if needed.
innodb_buffer_pool_size = 2G
# innodb_log_file_size = 48M         # REMOVED - Deprecated
innodb_redo_log_capacity = 512M       # REPLACED - Based on warning log calculation (48M*2 files)
innodb_log_buffer_size = 128M
innodb_file_per_table = 1
innodb_flush_method = O_DIRECT
innodb_read_io_threads = 16
innodb_write_io_threads = 16
innodb_io_capacity = 2000
innodb_io_capacity_max = 4000
# (Other InnoDB settings omitted for brevity unless needed for minimal startup)

# -- Network Settings --
max_allowed_packet = 512M
# net_buffer_length = 16K # Default is usually fine
back_log = 1024
interactive_timeout = 300
wait_timeout = 300

# -- Optimizer Settings --
optimizer_switch = 'index_merge=on,index_merge_union=on,index_merge_sort_union=on,index_merge_intersection=on,engine_condition_pushdown=on,index_condition_pushdown=on,mrr=on,mrr_cost_based=on,block_nested_loop=on,batched_key_access=on,materialization=on,semijoin=on,loosescan=on,firstmatch=on,duplicateweedout=on,subquery_materialization_cost_based=on,use_index_extensions=on,condition_fanout_filter=on,derived_merge=on'
optimizer_search_depth = 0
range_optimizer_max_mem_size = 0 # Unlimited, or set e.g. 512M for safety
max_execution_time = 60000      # 60 seconds limit for SELECTs

# -- Other Performance Tweaks --
performance_schema = OFF          # Disable for performance boost (loses diagnostics)
# max_prepared_stmt_count = 16384 # Default is usually fine

# -- Filesystem / Socket --
socket = /var/run/mysqld/mysqld.sock
# pid-file = /var/run/mysqld/mysqld.pid # Default location often used. Ensure directory permissions are secure (e.g., 750, owned by mysql:mysql) - FIX OUTSIDE my.cnf!
# tmpdir = /path/to/fast/tmpfs

# -- Security & Plugins --
# ssl = off                         # REMOVED - Deprecated
tls-version = ''                  # REPLACED - Correct way to disable SSL/TLS
skip-mysqlx                       # Disable X Plugin to save resources and prevent SSL error

[mysql]
# Client default character set
default-character-set = utf8mb4

[client]
# Client default character set
default-character-set = utf8mb4
socket = /var/run/mysqld/mysqld.sock